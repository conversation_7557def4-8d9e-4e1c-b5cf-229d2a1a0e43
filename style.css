* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.controls {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 600;
    color: #555;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    background: #667eea;
    color: white;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

button:hover {
    background: #5a67d8;
}

button:active {
    transform: translateY(1px);
}

.info {
    color: #666;
    font-size: 0.9em;
}

.info-panel {
    grid-column: 1 / -1;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.info-panel ul {
    margin-left: 20px;
    margin-top: 10px;
}

.info-panel li {
    margin-bottom: 5px;
}

.map-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

#mapCanvas {
    border: 2px solid #ddd;
    border-radius: 6px;
    cursor: crosshair;
    max-width: 100%;
    height: auto;
}

.status {
    text-align: center;
    margin-top: 20px;
    color: white;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

#plateCountValue,
#landPlatesCount,
#waterPercentage {
    font-weight: 600;
    color: #667eea;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .controls {
        grid-template-columns: 1fr;
    }
    
    #mapCanvas {
        width: 100%;
        height: auto;
    }
}
