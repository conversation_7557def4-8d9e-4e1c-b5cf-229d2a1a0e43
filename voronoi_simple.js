/**
 * Proper Voronoi diagram generator using Lloyd's relaxation
 * Creates natural-looking, non-overlapping polygonal cells
 */

class VoronoiGenerator {
    constructor(width, height) {
        this.width = width;
        this.height = height;
        this.sites = [];
        this.cells = [];
    }

    /**
     * Generate simple Voronoi diagram - just one layer, no wrapping
     */
    generateSeamless(numSites) {
        this.sites = [];
        this.cells = [];

        // Generate random sites - just one set, no duplicates
        this.sites = this.generateRandomSites(numSites);

        // Generate Voronoi cells using a simple approach
        this.cells = this.generateSimpleVoronoiCells();

        return this.cells;
    }

    generateRandomSites(numSites) {
        const sites = [];
        for (let i = 0; i < numSites; i++) {
            sites.push({
                x: Math.random() * this.width,
                y: Math.random() * this.height,
                id: i,
                isLand: Math.random() < 0.25 // 25% land
            });
        }
        return sites;
    }

    /**
     * Generate simple Voronoi cells - one clean layer
     */
    generateSimpleVoronoiCells() {
        const cells = [];

        for (const site of this.sites) {
            const boundaries = this.computeSimpleCellBoundary(site);
            cells.push({
                site: site,
                boundaries: boundaries,
                neighbors: []
            });
        }

        return cells;
    }

    /**
     * Compute simple cell boundary using ray casting
     */
    computeSimpleCellBoundary(targetSite) {
        const edgePoints = [];
        const numRays = 32; // Fewer rays for simpler shapes
        const maxDistance = Math.min(this.width, this.height) / 3;

        for (let i = 0; i < numRays; i++) {
            const angle = (i / numRays) * 2 * Math.PI;

            // Cast a ray from the site outward
            let distance = 10; // Start a bit away from the site
            const stepSize = 5;

            while (distance < maxDistance) {
                const x = targetSite.x + Math.cos(angle) * distance;
                const y = targetSite.y + Math.sin(angle) * distance;

                // Keep within canvas bounds
                if (x < 0 || x >= this.width || y < 0 || y >= this.height) {
                    edgePoints.push({ x: Math.max(0, Math.min(this.width, x)),
                                    y: Math.max(0, Math.min(this.height, y)) });
                    break;
                }

                const closestSite = this.findClosestSite(x, y);

                // If we hit a different site's territory, we found the boundary
                if (closestSite.id !== targetSite.id) {
                    edgePoints.push({ x, y });
                    break;
                }

                distance += stepSize;
            }

            // If we reached max distance without hitting another site
            if (distance >= maxDistance) {
                const x = targetSite.x + Math.cos(angle) * (maxDistance * 0.8);
                const y = targetSite.y + Math.sin(angle) * (maxDistance * 0.8);
                edgePoints.push({
                    x: Math.max(0, Math.min(this.width, x)),
                    y: Math.max(0, Math.min(this.height, y))
                });
            }
        }

        return edgePoints;
    }

    /**
     * Find the closest site to a given point
     */
    findClosestSite(x, y) {
        let closestSite = this.sites[0];
        let minDistance = this.distance(x, y, closestSite.x, closestSite.y);

        for (const site of this.sites) {
            const dist = this.distance(x, y, site.x, site.y);
            if (dist < minDistance) {
                minDistance = dist;
                closestSite = site;
            }
        }

        return closestSite;
    }

    /**
     * Find which cell contains a given point
     */
    findCellAtPoint(x, y) {
        const closestSite = this.findClosestSite(x, y);
        return this.cells.find(cell => cell.site.id === closestSite.id);
    }

    distance(x1, y1, x2, y2) {
        const dx = x1 - x2;
        const dy = y1 - y2;
        return Math.sqrt(dx * dx + dy * dy);
    }
}
