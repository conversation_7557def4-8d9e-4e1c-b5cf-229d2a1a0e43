<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tectonic Plate Map Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Tectonic Plate Map Generator</h1>
            <p>A scientifically-inspired map generator for worldbuilding</p>
        </header>
        
        <div class="controls">
            <div class="control-group">
                <label for="plateCount">Number of Plates (20-40):</label>
                <input type="range" id="plateCount" min="20" max="40" value="30">
                <span id="plateCountValue">30</span>
            </div>
            
            <div class="control-group">
                <label for="landPlates">Land Plates:</label>
                <span id="landPlatesCount">7</span>
                <span class="info">(Water: <span id="waterPercentage">77%</span>)</span>
            </div>
            
            <div class="control-group">
                <button id="generateBtn">Generate New Plates</button>
                <button id="resetBtn">Reset to Default</button>
            </div>
            
            <div class="info-panel">
                <p><strong>Instructions:</strong></p>
                <ul>
                    <li>Click on any plate to toggle between land (brown) and water (blue)</li>
                    <li>Water plates must be between 60-80% of total plates</li>
                    <li>The map tiles seamlessly in all directions</li>
                </ul>
            </div>
        </div>
        
        <div class="map-container">
            <canvas id="mapCanvas" width="800" height="600"></canvas>
        </div>
        
        <div class="status">
            <span id="statusMessage">Click Generate New Plates to start</span>
        </div>
    </div>
    
    <script src="voronoi_simple.js"></script>
    <script src="script.js"></script>
</body>
</html>
