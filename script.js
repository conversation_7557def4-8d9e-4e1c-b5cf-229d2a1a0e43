/**
 * Tectonic Plate Map Generator
 * Main application logic for generating and managing tectonic plates
 */

class TectonicPlateGenerator {
    constructor() {
        this.canvas = document.getElementById('mapCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.voronoi = new VoronoiGenerator(this.canvas.width, this.canvas.height);
        this.plates = [];
        this.currentPlateCount = 30;
        this.landPlateCount = 7;
        
        this.initializeEventListeners();
        this.updateUI();
    }

    initializeEventListeners() {
        // Plate count slider
        const plateCountSlider = document.getElementById('plateCount');
        const plateCountValue = document.getElementById('plateCountValue');
        
        plateCountSlider.addEventListener('input', (e) => {
            this.currentPlateCount = parseInt(e.target.value);
            plateCountValue.textContent = this.currentPlateCount;
        });

        // Generate button
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generatePlates();
        });

        // Reset button
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetToDefault();
        });

        // Canvas click for toggling land/water
        this.canvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });
    }

    generatePlates() {
        this.updateStatus('Generating tectonic plates...');
        
        // Generate Voronoi cells
        this.plates = this.voronoi.generateSeamless(this.currentPlateCount);
        
        // Apply continental clustering for initial land placement
        this.applyInitialLandDistribution();
        
        // Render the map
        this.renderMap();
        
        this.updateUI();
        this.updateStatus('Tectonic plates generated! Click on plates to toggle land/water.');
    }

    applyInitialLandDistribution() {
        // Reset all plates to water
        this.plates.forEach(plate => {
            plate.site.isLand = false;
        });

        // Create 2-3 continental clusters
        const continents = this.createContinentalClusters();
        
        let landPlatesAssigned = 0;
        
        // Assign land plates to continents
        for (const continent of continents) {
            const continentSize = Math.floor(this.landPlateCount / continents.length);
            const remainingPlates = this.landPlateCount - landPlatesAssigned;
            const platesToAssign = Math.min(continentSize, remainingPlates);
            
            this.assignLandToContinent(continent, platesToAssign);
            landPlatesAssigned += platesToAssign;
            
            if (landPlatesAssigned >= this.landPlateCount) break;
        }

        // Assign any remaining land plates to the largest continent
        if (landPlatesAssigned < this.landPlateCount) {
            const remainingToAssign = this.landPlateCount - landPlatesAssigned;
            this.assignAdditionalLandPlates(remainingToAssign);
        }
    }

    createContinentalClusters() {
        const continents = [];
        const numContinents = Math.floor(Math.random() * 2) + 2; // 2-3 continents
        
        // Select random seed points for continents
        for (let i = 0; i < numContinents; i++) {
            const seedPlate = this.plates[Math.floor(Math.random() * this.plates.length)];
            continents.push({
                center: seedPlate.site,
                plates: [seedPlate]
            });
        }
        
        return continents;
    }

    assignLandToContinent(continent, numPlates) {
        const assigned = new Set();
        const queue = [continent.center];
        assigned.add(continent.center.id);
        continent.center.isLand = true;
        
        let assignedCount = 1;
        
        while (assignedCount < numPlates && queue.length > 0) {
            const currentSite = queue.shift();
            const neighbors = this.findNeighboringSites(currentSite);
            
            // Sort neighbors by distance to continent center
            neighbors.sort((a, b) => {
                const distA = this.distance(a.x, a.y, continent.center.x, continent.center.y);
                const distB = this.distance(b.x, b.y, continent.center.x, continent.center.y);
                return distA - distB;
            });
            
            for (const neighbor of neighbors) {
                if (assignedCount >= numPlates) break;
                if (assigned.has(neighbor.id)) continue;
                
                neighbor.isLand = true;
                assigned.add(neighbor.id);
                queue.push(neighbor);
                assignedCount++;
            }
        }
    }

    assignAdditionalLandPlates(numPlates) {
        const landPlates = this.plates.filter(plate => plate.site.isLand);
        let assigned = 0;
        
        for (const landPlate of landPlates) {
            if (assigned >= numPlates) break;
            
            const neighbors = this.findNeighboringSites(landPlate.site);
            for (const neighbor of neighbors) {
                if (assigned >= numPlates) break;
                if (!neighbor.isLand) {
                    neighbor.isLand = true;
                    assigned++;
                }
            }
        }
    }

    findNeighboringSites(site) {
        const neighbors = [];
        const searchRadius = Math.min(this.canvas.width, this.canvas.height) / 8;
        
        for (const plate of this.plates) {
            if (plate.site.id === site.id) continue;
            
            const dist = this.distance(site.x, site.y, plate.site.x, plate.site.y);
            if (dist <= searchRadius) {
                neighbors.push(plate.site);
            }
        }
        
        return neighbors;
    }

    distance(x1, y1, x2, y2) {
        const dx = x1 - x2;
        const dy = y1 - y2;
        return Math.sqrt(dx * dx + dy * dy);
    }

    handleCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (event.clientX - rect.left) * (this.canvas.width / rect.width);
        const y = (event.clientY - rect.top) * (this.canvas.height / rect.height);

        // Find which plate was clicked
        const clickedCell = this.voronoi.findCellAtPoint(x, y);

        if (clickedCell) {
            const currentLandCount = this.plates.filter(plate => plate.site.isLand).length;
            const wouldBeLandCount = clickedCell.site.isLand ? currentLandCount - 1 : currentLandCount + 1;
            const waterPercentage = ((this.currentPlateCount - wouldBeLandCount) / this.currentPlateCount) * 100;

            // Check if the change would violate the 60-80% water rule
            if (waterPercentage >= 60 && waterPercentage <= 80) {
                clickedCell.site.isLand = !clickedCell.site.isLand;
                this.landPlateCount = wouldBeLandCount;
                this.renderMap();
                this.updateUI();
                this.updateStatus(`Plate toggled! Water: ${waterPercentage.toFixed(1)}%`);
            } else {
                this.updateStatus(`Cannot toggle: Water must be between 60-80% (would be ${waterPercentage.toFixed(1)}%)`);
            }
        }
    }

    renderMap() {
        // Clear canvas
        this.ctx.fillStyle = '#f0f0f0';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw plate fills
        for (const plate of this.plates) {
            this.drawPlate(plate);
        }

        // Draw plate boundaries
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 2;

        for (const plate of this.plates) {
            this.drawPlateBoundary(plate);
        }
    }

    drawPlate(plate) {
        if (plate.boundaries.length < 3) return;

        this.ctx.fillStyle = plate.site.isLand ? '#8B4513' : '#4169E1'; // Brown for land, blue for water

        this.ctx.beginPath();
        this.ctx.moveTo(plate.boundaries[0].x, plate.boundaries[0].y);

        for (let i = 1; i < plate.boundaries.length; i++) {
            this.ctx.lineTo(plate.boundaries[i].x, plate.boundaries[i].y);
        }

        this.ctx.closePath();
        this.ctx.fill();
    }

    drawPlateBoundary(plate) {
        if (plate.boundaries.length < 3) return;

        this.ctx.beginPath();
        this.ctx.moveTo(plate.boundaries[0].x, plate.boundaries[0].y);

        for (let i = 1; i < plate.boundaries.length; i++) {
            this.ctx.lineTo(plate.boundaries[i].x, plate.boundaries[i].y);
        }

        this.ctx.closePath();
        this.ctx.stroke();
    }

    updateUI() {
        const landCount = this.plates.filter(plate => plate.site.isLand).length;
        const waterPercentage = ((this.currentPlateCount - landCount) / this.currentPlateCount) * 100;

        document.getElementById('landPlatesCount').textContent = landCount;
        document.getElementById('waterPercentage').textContent = `${waterPercentage.toFixed(1)}%`;
    }

    updateStatus(message) {
        document.getElementById('statusMessage').textContent = message;
    }

    resetToDefault() {
        this.currentPlateCount = 30;
        this.landPlateCount = 7;

        document.getElementById('plateCount').value = 30;
        document.getElementById('plateCountValue').textContent = '30';

        this.generatePlates();
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TectonicPlateGenerator();
});
