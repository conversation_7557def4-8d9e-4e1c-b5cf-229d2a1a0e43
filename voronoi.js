/**
 * Simple grid-based plate generator with seamless tiling support
 * Creates clean, non-overlapping rectangular cells that look like tectonic plates
 */

class VoronoiGenerator {
    constructor(width, height) {
        this.width = width;
        this.height = height;
        this.sites = [];
        this.cells = [];
        this.gridSize = 80; // Size of each grid cell
    }

    /**
     * Generate seamless grid-based plates
     */
    generateSeamless(numSites) {
        this.sites = [];
        this.cells = [];

        // Calculate grid dimensions
        const cols = Math.ceil(this.width / this.gridSize);
        const rows = Math.ceil(this.height / this.gridSize);

        // Create grid-based plates
        let id = 0;
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = col * this.gridSize + this.gridSize / 2;
                const y = row * this.gridSize + this.gridSize / 2;

                // Randomly determine if this is land or water
                const isLand = Math.random() < 0.3; // 30% land, 70% water

                const site = {
                    x: x,
                    y: y,
                    id: id++,
                    isLand: isLand,
                    isCenterTile: true
                };

                this.sites.push(site);

                // Create rectangular cell for this site
                const cell = this.createRectangularCell(site, col, row);
                this.cells.push(cell);
            }
        }

        return this.cells;
    }

    generateRandomSites(numSites) {
        const sites = [];
        for (let i = 0; i < numSites; i++) {
            sites.push({
                x: Math.random() * this.width,
                y: Math.random() * this.height,
                id: i,
                isLand: false // Will be set later based on clustering
            });
        }
        return sites;
    }



    /**
     * Generate proper Voronoi cells using a grid-based approach
     */
    generateProperVoronoiCells() {
        const cells = [];
        const centerSites = this.sites.filter(site => site.isCenterTile);

        // For each center site, compute its Voronoi cell
        for (const site of centerSites) {
            const boundaries = this.computeVoronoiCell(site);
            const cell = {
                site: site,
                boundaries: boundaries,
                neighbors: []
            };
            cells.push(cell);
        }

        return cells;
    }

    /**
     * Compute the Voronoi cell for a specific site
     */
    computeVoronoiCell(targetSite) {
        const boundaries = [];
        const step = 8; // Grid resolution
        const boundaryPoints = new Set();

        // Find all boundary points for this cell
        for (let x = 0; x < this.width; x += step) {
            for (let y = 0; y < this.height; y += step) {
                const closestSite = this.findClosestSite(x, y);

                // If this point belongs to our target site
                if (closestSite.id === targetSite.id && closestSite.isCenterTile) {
                    // Check if it's a boundary point
                    if (this.isBoundaryPoint(x, y, targetSite, step)) {
                        boundaryPoints.add(`${x},${y}`);
                    }
                }
            }
        }

        // Convert to array and sort into polygon
        const points = Array.from(boundaryPoints).map(str => {
            const [x, y] = str.split(',').map(Number);
            return { x, y };
        });

        if (points.length < 3) {
            // Fallback to simple polygon
            return this.createFallbackPolygon(targetSite);
        }

        return this.sortPointsToPolygon(points, targetSite);
    }

    /**
     * Check if a point is on the boundary of a Voronoi cell
     */
    isBoundaryPoint(x, y, targetSite, step) {
        const neighbors = [
            [x - step, y], [x + step, y],
            [x, y - step], [x, y + step]
        ];

        for (const [nx, ny] of neighbors) {
            // Wrap coordinates for seamless tiling
            const wx = ((nx % this.width) + this.width) % this.width;
            const wy = ((ny % this.height) + this.height) % this.height;

            const neighborSite = this.findClosestSite(wx, wy);

            // If neighbor belongs to different site, this is a boundary
            if (neighborSite.id !== targetSite.id || !neighborSite.isCenterTile) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create a fallback polygon when boundary detection fails
     */
    createFallbackPolygon(site) {
        const numSides = 6;
        const radius = Math.min(this.width, this.height) / 20;
        const points = [];

        for (let i = 0; i < numSides; i++) {
            const angle = (i / numSides) * 2 * Math.PI;
            const x = site.x + Math.cos(angle) * radius;
            const y = site.y + Math.sin(angle) * radius;
            points.push({ x, y });
        }

        return points;
    }

    /**
     * Sort points into a proper polygon
     */
    sortPointsToPolygon(points, centerSite) {
        if (points.length < 3) return points;

        // Calculate centroid
        const centroid = {
            x: points.reduce((sum, p) => sum + p.x, 0) / points.length,
            y: points.reduce((sum, p) => sum + p.y, 0) / points.length
        };

        // Sort by angle from centroid
        return points.sort((a, b) => {
            const angleA = Math.atan2(a.y - centroid.y, a.x - centroid.x);
            const angleB = Math.atan2(b.y - centroid.y, b.x - centroid.x);
            return angleA - angleB;
        });
    }

    findClosestSite(x, y) {
        let closestSite = this.sites[0];
        let minDistance = this.distance(x, y, closestSite.x, closestSite.y);

        for (const site of this.sites) {
            const dist = this.distance(x, y, site.x, site.y);
            if (dist < minDistance) {
                minDistance = dist;
                closestSite = site;
            }
        }

        return closestSite;
    }



    distance(x1, y1, x2, y2) {
        const dx = x1 - x2;
        const dy = y1 - y2;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Find which cell contains a given point
     */
    findCellAtPoint(x, y) {
        const closestSite = this.findClosestSite(x, y);
        return this.cells.find(cell =>
            cell.site.id === closestSite.id && closestSite.isCenterTile
        );
    }
}
