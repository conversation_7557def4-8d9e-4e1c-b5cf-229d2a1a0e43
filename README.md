# Tectonic Plate Map Generator

A scientifically-inspired map generator for worldbuilding and fiction, starting with realistic tectonic plate generation.

## Features

### Current Implementation
- **Seamless Tectonic Plates**: Generates 20-40 tectonic plates using Voronoi diagrams
- **Infinite Tiling**: The map seamlessly wraps in all directions (like a planet surface)
- **Continental Clustering**: Land plates are intelligently grouped into 2-3 continents
- **Interactive Editing**: Click on any plate to toggle between land and water
- **Scientific Constraints**: Maintains 60-80% water coverage (Earth-like)
- **Real-time Validation**: Prevents invalid land/water distributions

### Technical Details
- Uses Voronoi diagrams for realistic plate boundaries
- Implements seamless wrapping by generating a 3x3 grid of points
- Continental clustering algorithm ensures realistic landmass distribution
- HTML5 Canvas for smooth rendering and interaction

## Usage

1. Open `index.html` in a web browser
2. Click "Generate New Plates" to create a new tectonic plate configuration
3. Use the slider to adjust the number of plates (20-40)
4. Click on individual plates to toggle between land (brown) and water (blue)
5. The system enforces the 60-80% water rule automatically

## Controls

- **Plate Count Slider**: Adjust the total number of tectonic plates
- **Generate New Plates**: Create a completely new plate configuration
- **Reset to Default**: Return to the default 30 plates with 7 land plates
- **Click Plates**: Toggle individual plates between land and water

## Scientific Accuracy

The generator follows Earth-like rules:
- **Plate Count**: 20-40 plates (Earth has ~15-20 major plates)
- **Water Coverage**: 60-80% (Earth is ~71% water)
- **Continental Distribution**: Land plates cluster into realistic continents
- **Seamless Wrapping**: Represents a spherical planet surface

## Future Enhancements

This is the foundation for a comprehensive map generator. Planned features include:
- Plate tectonics simulation (collision, subduction, spreading)
- Mountain range generation at plate boundaries
- Ocean depth and continental shelf modeling
- Climate simulation based on geography
- Biome distribution
- River and watershed systems
- Realistic coastline generation

## File Structure

```
mapgen/
├── index.html          # Main HTML interface
├── style.css           # Styling and responsive design
├── script.js           # Main application logic
├── voronoi.js          # Voronoi diagram generation with seamless tiling
└── README.md           # This documentation
```

## Browser Compatibility

Works in all modern browsers that support:
- HTML5 Canvas
- ES6 Classes
- CSS Grid

## Getting Started

Simply open `index.html` in your web browser - no build process or dependencies required!

## Contributing

This project is designed to be scientifically accurate while remaining accessible for worldbuilding. Contributions that enhance realism or add new geographic features are welcome.
